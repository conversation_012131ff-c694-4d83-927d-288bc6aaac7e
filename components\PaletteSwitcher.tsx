'use client';

import React, { useState, useEffect } from 'react';

interface Palette {
  id: string;
  name: string;
  description: string;
  colors: {
    mainBackground: string;
    ctaButton: string;
    logo: string;
    serviceCardsHover: string;
    textHighlights: string;
    secondaryElements: string;
    bodyBackground?: string;
    textColor?: string;
  };
}

const palettes: Palette[] = [
  {
    id: 'A',
    name: 'Original',
    description: 'Current IMPAXX palette',
    colors: {
      mainBackground: '#0b1a24',
      ctaButton: 'linear-gradient(135deg, #FF6B00 0%, #FF8124 100%)',
      logo: 'linear-gradient(135deg, #FAFAFA 0%, #8B8B8B 30%, #FF6B00 65%, #FAFAFA 100%)',
      serviceCardsHover: 'rgba(255, 107, 0, 0.08)',
      textHighlights: 'linear-gradient(135deg, #FF6B00, #FF8124)',
      secondaryElements: 'linear-gradient(135deg, #132531 0%, #1f3544 100%)',
      bodyBackground: '#0b1a24',
      textColor: '#d8d8d8'
    }
  },
  {
    id: 'B',
    name: 'Executive',
    description: 'Ultra-sophisticated executive palette',
    colors: {
      mainBackground: 'linear-gradient(135deg, #0B0B0B 0%, #1A1A1A 25%, #2D2D2D 50%, #4A4A4A 75%, #D4AF37 100%)',
      ctaButton: 'linear-gradient(135deg, #D4AF37 0%, #B8941F 50%, #8B8B8B 100%)',
      logo: 'linear-gradient(135deg, #FAFAFA 0%, #8B8B8B 30%, #D4AF37 65%, #FAFAFA 100%)',
      serviceCardsHover: 'rgba(212, 175, 55, 0.08)',
      textHighlights: 'linear-gradient(135deg, #D4AF37, #B8941F, #8B8B8B)',
      secondaryElements: 'linear-gradient(135deg, #1A1A1A 0%, #2D2D2D 50%, #4A4A4A 100%)',
      bodyBackground: '#0B0B0B',
      textColor: '#FAFAFA'
    }
  },
  {
    id: 'C',
    name: 'Deep Space',
    description: 'IMPAXX deep space universe palette',
    colors: {
      mainBackground: 'linear-gradient(135deg, #0A0A0A 0%, #1E293B 25%, #334155 50%, #475569 75%, #0EA5E9 100%)',
      ctaButton: 'linear-gradient(135deg, #0EA5E9 0%, #0284C7 50%, #64748B 100%)',
      logo: 'linear-gradient(135deg, #F8FAFC 0%, #64748B 30%, #0EA5E9 65%, #F8FAFC 100%)',
      serviceCardsHover: 'rgba(14, 165, 233, 0.08)',
      textHighlights: 'linear-gradient(135deg, #0EA5E9, #14B8A6, #0284C7)',
      secondaryElements: 'linear-gradient(135deg, #0A0A0A 0%, #0F172A 25%, #1E293B 50%, #334155 75%, #14B8A6 100%)',
      bodyBackground: 'linear-gradient(135deg, #0A0A0A 0%, #1E293B 25%, #334155 50%, #475569 75%, #0EA5E9 100%)',
      textColor: '#F8FAFC'
    }
  },
  {
    id: 'D',
    name: 'Coming Soon',
    description: 'Palette D - To be defined',
    colors: {
      mainBackground: '#0b1a24',
      ctaButton: 'linear-gradient(135deg, #FF6B00 0%, #FF8124 100%)',
      logo: 'linear-gradient(135deg, #FAFAFA 0%, #8B8B8B 30%, #FF6B00 65%, #FAFAFA 100%)',
      serviceCardsHover: 'rgba(255, 107, 0, 0.08)',
      textHighlights: 'linear-gradient(135deg, #FF6B00, #FF8124)',
      secondaryElements: 'linear-gradient(135deg, #132531 0%, #1f3544 100%)',
      bodyBackground: '#0b1a24',
      textColor: '#d8d8d8'
    }
  }
];

const PaletteSwitcher: React.FC = () => {
  const [currentPalette, setCurrentPalette] = useState<string>('A');
  const [isOpen, setIsOpen] = useState<boolean>(false);

  useEffect(() => {
    // Load saved palette from localStorage
    const savedPalette = localStorage.getItem('impaxx-palette');
    if (savedPalette && palettes.find(p => p.id === savedPalette)) {
      setCurrentPalette(savedPalette);
    }
  }, []);

  useEffect(() => {
    // Apply the selected palette
    const palette = palettes.find(p => p.id === currentPalette);
    if (palette) {
      applyPalette(palette);
      localStorage.setItem('impaxx-palette', currentPalette);
    }
  }, [currentPalette]);

  const applyPalette = (palette: Palette) => {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--main-background', palette.colors.mainBackground);
    root.style.setProperty('--cta-button', palette.colors.ctaButton);
    root.style.setProperty('--logo-gradient', palette.colors.logo);
    root.style.setProperty('--service-cards-hover', palette.colors.serviceCardsHover);
    root.style.setProperty('--text-highlights', palette.colors.textHighlights);
    root.style.setProperty('--secondary-elements', palette.colors.secondaryElements);
    
    // Apply body styles
    if (palette.colors.bodyBackground) {
      document.body.style.backgroundColor = palette.colors.bodyBackground;
    }
    if (palette.colors.textColor) {
      document.body.style.color = palette.colors.textColor;
    }

    // Add palette class to body for additional styling
    document.body.className = document.body.className.replace(/palette-[A-D]/g, '');
    document.body.classList.add(`palette-${palette.id}`);
  };

  const handlePaletteChange = (paletteId: string) => {
    setCurrentPalette(paletteId);
    setIsOpen(false);
  };

  const currentPaletteData = palettes.find(p => p.id === currentPalette);

  return (
    <div className="palette-switcher">
      <button 
        className="palette-toggle"
        onClick={() => setIsOpen(!isOpen)}
        title="Switch Color Palette"
      >
        <span className="palette-icon">🎨</span>
        <span className="palette-label">Palette {currentPalette}</span>
      </button>
      
      {isOpen && (
        <div className="palette-menu">
          <div className="palette-header">
            <h3>Color Palettes</h3>
            <button 
              className="close-btn"
              onClick={() => setIsOpen(false)}
            >
              ×
            </button>
          </div>
          
          <div className="palette-options">
            {palettes.map((palette) => (
              <div
                key={palette.id}
                className={`palette-option ${currentPalette === palette.id ? 'active' : ''}`}
                onClick={() => handlePaletteChange(palette.id)}
              >
                <div className="palette-preview">
                  <div 
                    className="color-sample main"
                    style={{ background: palette.colors.mainBackground }}
                  ></div>
                  <div 
                    className="color-sample cta"
                    style={{ background: palette.colors.ctaButton }}
                  ></div>
                  <div 
                    className="color-sample logo"
                    style={{ background: palette.colors.logo }}
                  ></div>
                </div>
                <div className="palette-info">
                  <div className="palette-name">
                    <strong>Palette {palette.id}: {palette.name}</strong>
                  </div>
                  <div className="palette-description">
                    {palette.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <style jsx>{`
        .palette-switcher {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1000;
          font-family: 'Segoe UI', sans-serif;
        }
        
        .palette-toggle {
          background: rgba(0, 0, 0, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          padding: 8px 12px;
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }
        
        .palette-toggle:hover {
          background: rgba(0, 0, 0, 0.9);
          border-color: rgba(255, 255, 255, 0.4);
          transform: translateY(-1px);
        }
        
        .palette-icon {
          font-size: 16px;
        }
        
        .palette-menu {
          position: absolute;
          top: 100%;
          right: 0;
          margin-top: 8px;
          background: rgba(0, 0, 0, 0.95);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          padding: 16px;
          min-width: 320px;
          backdrop-filter: blur(20px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }
        
        .palette-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .palette-header h3 {
          margin: 0;
          color: white;
          font-size: 16px;
        }
        
        .close-btn {
          background: none;
          border: none;
          color: white;
          font-size: 20px;
          cursor: pointer;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          transition: background 0.2s ease;
        }
        
        .close-btn:hover {
          background: rgba(255, 255, 255, 0.1);
        }
        
        .palette-options {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
        
        .palette-option {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          border: 1px solid transparent;
        }
        
        .palette-option:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 255, 255, 0.1);
        }
        
        .palette-option.active {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
        }
        
        .palette-preview {
          display: flex;
          gap: 4px;
          flex-shrink: 0;
        }
        
        .color-sample {
          width: 16px;
          height: 16px;
          border-radius: 3px;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .palette-info {
          flex: 1;
        }
        
        .palette-name {
          color: white;
          font-size: 14px;
          margin-bottom: 2px;
        }
        
        .palette-description {
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
          line-height: 1.3;
        }
      `}</style>
    </div>
  );
};

export default PaletteSwitcher;

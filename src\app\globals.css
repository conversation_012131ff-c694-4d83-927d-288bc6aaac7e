@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground: #ffffff;
  --background: #0A0E13;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Segoe UI', sans-serif;
}

/* Main layout */
.container {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Navigation */
.side-nav {
  @apply fixed left-0 top-1/4 flex flex-col gap-6 p-4 z-10;
}

.nav-link {
  @apply text-text-light hover:text-primary transition-colors duration-200 text-sm;
}

/* Logo */
.logo-container {
  @apply flex justify-center my-12;
}

.logo {
  @apply w-48 h-auto;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  @apply font-bold text-text-lighter;
}

.tagline {
  @apply text-4xl font-bold uppercase tracking-wide mb-8 leading-tight;
}

.tagline-highlight {
  @apply text-primary;
}

/* Buttons */
.btn-primary {
  @apply bg-primary hover:bg-primary-hover text-white font-bold py-2 px-6 rounded transition-colors duration-200;
}

/* Sections */
.section-title {
  @apply text-2xl uppercase mb-8 text-center font-bold;
}

/* Service Boxes */
@layer components {
  .services-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .service-box {
    @apply bg-dark-lighter border border-dark-border rounded-lg p-6 transition-all duration-300 shadow-md text-center;
  }

  .service-box:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  .service-icon {
    @apply text-2xl mb-4 text-primary inline-flex items-center justify-center w-16 h-16 rounded-full bg-dark border border-dark-border;
  }

  .service-title {
    @apply text-lg font-bold mb-2;
  }

  .service-description {
    @apply text-sm text-text-light;
  }
}

/* CTA Section */
.cta-section {
  @apply text-center mt-20 bg-dark-lighter py-10 px-6 rounded-lg;
}

/* Footer */
.footer {
  @apply w-full py-8 mt-16 text-center text-sm text-gray-400 border-t border-dark-border;
}

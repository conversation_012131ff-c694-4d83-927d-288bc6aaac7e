'use client';

import React, { useEffect, useRef } from 'react';

const Particles: React.FC = () => {
  const particlesRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const createParticles = () => {
      if (!particlesRef.current) return;
      
      // Clear existing particles
      particlesRef.current.innerHTML = '';
      
      const particleCount = window.innerWidth > 768 ? 40 : 20;
      
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 8 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
        
        // Add random colors to some particles based on current palette
        if (Math.random() > 0.7) {
          let colors = ['rgba(139, 92, 246, 0.6)', 'rgba(6, 182, 212, 0.6)', 'rgba(236, 72, 153, 0.6)'];

          // Check current palette
          const bodyClass = document.body.className;
          if (bodyClass.includes('palette-B')) {
            colors = ['rgba(212, 175, 55, 0.6)', 'rgba(184, 148, 31, 0.6)', 'rgba(139, 139, 139, 0.6)'];
          } else if (bodyClass.includes('palette-C')) {
            colors = ['rgba(14, 165, 233, 0.6)', 'rgba(20, 184, 166, 0.6)', 'rgba(2, 132, 199, 0.6)'];
          }

          particle.style.background = colors[Math.floor(Math.random() * colors.length)];
          particle.style.boxShadow = `0 0 10px ${particle.style.background}`;
        }
        
        particlesRef.current.appendChild(particle);
      }
    };

    // Create particles on mount
    createParticles();

    // Recreate particles on resize
    const handleResize = () => {
      createParticles();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return <div ref={particlesRef} className="particles" id="particles"></div>;
};

export default Particles;

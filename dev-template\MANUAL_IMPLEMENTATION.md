# Manual Implementation Guide

If you prefer to implement the development environment manually, follow these steps:

## Step 1: Create Folder Structure

Create the following folders:

```
/dev
/dev/images
/dev/icons
/components
/public/images
/public/icons
```

## Step 2: Create DevIndicator Component

Create a file at `components/DevIndicator.tsx` with the following content:

```tsx
import React from 'react';

const DevIndicator = () => {
  // Only show in development environment
  if (process.env.NODE_ENV !== 'development' && process.env.NEXT_PUBLIC_ENVIRONMENT !== 'development') {
    return null;
  }

  return (
    <div 
      style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        backgroundColor: '#FF6B00',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '4px',
        fontWeight: 'bold',
        zIndex: 9999,
        boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
      }}
    >
      DEVELOPMENT MODE
    </div>
  );
};

export default DevIndicator;
```

## Step 3: Create Environment Files

Create `.env.development`:

```
NEXT_PUBLIC_ENVIRONMENT=development
```

Create `.env.production`:

```
NEXT_PUBLIC_ENVIRONMENT=production
```

## Step 4: Create Development Mode Script

Create `dev-mode.js` in the root of your project:

```js
/**
 * Development Mode Helper Script
 * 
 * This script helps switch between development and production configurations
 * without affecting the live site.
 */

const fs = require('fs');
const path = require('path');

// Check command line arguments
const args = process.argv.slice(2);
const mode = args[0] || 'dev'; // Default to dev mode

if (mode === 'dev') {
  console.log('Switching to DEVELOPMENT mode...');
  
  // Use development config
  if (fs.existsSync('next.config.dev.js')) {
    const devConfig = fs.readFileSync('next.config.dev.js', 'utf8');
    fs.writeFileSync('next.config.js', devConfig);
    console.log('✅ Using development Next.js configuration');
  }
  
  // Set environment
  if (!fs.existsSync('.env.development')) {
    fs.writeFileSync('.env.development', 'NEXT_PUBLIC_ENVIRONMENT=development\n');
    console.log('✅ Created development environment file');
  }
  
  console.log('\n🚀 Development mode activated!');
  console.log('Run "npm run dev" to start the development server');
  
} else if (mode === 'prod') {
  console.log('Switching to PRODUCTION mode...');
  
  // Restore production config if it exists
  if (fs.existsSync('next.config.prod.js')) {
    const prodConfig = fs.readFileSync('next.config.prod.js', 'utf8');
    fs.writeFileSync('next.config.js', prodConfig);
    console.log('✅ Using production Next.js configuration');
  } else {
    // Create a basic production config
    const prodConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  trailingSlash: false,
  images: {
    domains: [],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
}

module.exports = nextConfig`;
    
    // Save the current config as prod config
    fs.writeFileSync('next.config.prod.js', prodConfig);
    fs.writeFileSync('next.config.js', prodConfig);
    console.log('✅ Created and using production Next.js configuration');
  }
  
  console.log('\n🚀 Production mode activated!');
  console.log('Run "npm run build" followed by "npm start" to use production mode');
  
} else {
  console.error('❌ Invalid mode. Use "dev" or "prod"');
  process.exit(1);
}
```

## Step 5: Create Next.js Development Config

Create `next.config.dev.js`:

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  trailingSlash: false,
  images: {
    domains: [],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Development-specific settings
  env: {
    ENVIRONMENT: 'development'
  }
}

module.exports = nextConfig
```

## Step 6: Update package.json

Add the following scripts to your package.json:

```json
"scripts": {
  "use:dev": "node dev-mode.js dev",
  "use:prod": "node dev-mode.js prod"
}
```

## Step 7: Install Dependencies

```bash
npm install cross-env --save-dev
```

## Step 8: Update Layout Component

Update your layout component to include the DevIndicator:

```jsx
import dynamic from 'next/dynamic';

// Dynamically import the DevIndicator to avoid SSR issues
const DevIndicator = dynamic(() => import('../components/DevIndicator'), {
  ssr: false
});

export default function Layout({ children }) {
  return (
    <div>
      {children}
      <DevIndicator />
    </div>
  );
}
```

## Step 9: Update .gitignore

Add the following to your .gitignore file:

```
# development files
/dev-temp/
*.dev.js
*.dev.ts
*.dev.tsx
.env.development
```

## Step 10: Switch to Development Mode

```bash
npm run use:dev
```

## Step 11: Start the Development Server

```bash
npm run dev
```

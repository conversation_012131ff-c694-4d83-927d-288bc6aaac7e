
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        'dark': '#0A0E13',
        'dark-lighter': '#132531',
        'dark-border': '#1f3544',
        'primary': '#FF6B00',  // Orange color for buttons and highlights
        'primary-hover': '#FF8124',
        'text-light': '#d8d8d8',
        'text-lighter': '#ffffff'
      },
      boxShadow: {
        'service': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      }
    },
  },
  plugins: [],
}

'use client';

import React, { useEffect } from 'react';

const InteractiveEffects: React.FC = () => {
  useEffect(() => {
    // Mouse interaction with service cards
    const handleMouseMove = (e: MouseEvent) => {
      const cards = document.querySelectorAll('.service-card');
      const mouseX = e.clientX;
      const mouseY = e.clientY;
      
      cards.forEach(card => {
        const rect = card.getBoundingClientRect();
        const cardX = rect.left + rect.width / 2;
        const cardY = rect.top + rect.height / 2;
        
        const deltaX = mouseX - cardX;
        const deltaY = mouseY - cardY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        
        if (distance < 300) {
          const intensity = (300 - distance) / 300;
          const rotateX = (deltaY / 300) * intensity * 10;
          const rotateY = (deltaX / 300) * intensity * 10;
          
          (card as HTMLElement).style.transform = `translateY(-20px) rotateX(${-rotateX}deg) rotateY(${rotateY}deg)`;
        } else {
          (card as HTMLElement).style.transform = '';
        }
      });
    };

    // Parallax effect for orbs
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const orbs = document.querySelectorAll('.orb');
      
      orbs.forEach((orb, index) => {
        const speed = (index + 1) * 0.3;
        (orb as HTMLElement).style.transform = `translateY(${scrolled * speed}px)`;
      });

      // Navbar scroll effect
      const nav = document.getElementById('nav');
      if (nav) {
        if (scrolled > 100) {
          nav.classList.add('scrolled');
        } else {
          nav.classList.remove('scrolled');
        }
      }
    };

    // Smooth scrolling for navigation
    const handleNavClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const targetElement = document.querySelector(target.getAttribute('href')!);
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    };

    // Intersection Observer for fade-in animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, observerOptions);

    // Observe fade-in elements
    const fadeInElements = document.querySelectorAll('.fade-in');
    fadeInElements.forEach(el => {
      observer.observe(el);
    });

    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    
    const handleMobileMenu = () => {
      if (navLinks) {
        const currentDisplay = window.getComputedStyle(navLinks).display;
        (navLinks as HTMLElement).style.display = currentDisplay === 'flex' ? 'none' : 'flex';
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', handleNavClick);
    });
    
    if (mobileMenuBtn) {
      mobileMenuBtn.addEventListener('click', handleMobileMenu);
    }

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.removeEventListener('click', handleNavClick);
      });
      
      if (mobileMenuBtn) {
        mobileMenuBtn.removeEventListener('click', handleMobileMenu);
      }
      
      observer.disconnect();
    };
  }, []);

  return null; // This component only adds event listeners
};

export default InteractiveEffects;

{"name": "impaxx-nextjs", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:safe": "cross-env NODE_ENV=development next dev", "build": "next build", "build:dev": "cross-env NODE_ENV=development next build", "start": "next start", "use:dev": "node dev-mode.js dev", "use:prod": "node dev-mode.js prod"}, "dependencies": {"autoprefixer": "^10.4.0", "next": "13.4.7", "postcss": "^8.4.0", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/node": "22.14.1", "@types/react": "19.1.1", "cross-env": "^7.0.3", "typescript": "5.8.3"}}
/**
 * Development Environment Setup Script
 * 
 * This script sets up the development environment structure for a Next.js project.
 * It creates the necessary files and folders, and updates package.json.
 */

const fs = require('fs');
const path = require('path');

console.log('Setting up development environment...');

// Create folders
const folders = [
  'dev',
  'dev/images',
  'dev/icons',
  'components',
  'public/images',
  'public/icons'
];

folders.forEach(folder => {
  if (!fs.existsSync(folder)) {
    fs.mkdirSync(folder, { recursive: true });
    console.log(`✅ Created folder: ${folder}`);
  } else {
    console.log(`ℹ️ Folder already exists: ${folder}`);
  }
});

// Copy files from template
const filesToCopy = [
  { src: 'DevIndicator.tsx', dest: 'components/DevIndicator.tsx' },
  { src: 'dev-mode.js', dest: 'dev-mode.js' },
  { src: 'next.config.dev.js', dest: 'next.config.dev.js' },
  { src: '.env.development', dest: '.env.development' },
  { src: '.env.production', dest: '.env.production' }
];

filesToCopy.forEach(file => {
  const templatePath = path.join(__dirname, file.src);
  if (fs.existsSync(templatePath)) {
    fs.copyFileSync(templatePath, file.dest);
    console.log(`✅ Copied file: ${file.dest}`);
  } else {
    console.error(`❌ Template file not found: ${templatePath}`);
  }
});

// Create README for dev folder
const devReadmeContent = `# Development Assets

This folder contains development assets that are not yet ready for production.

## Folder Structure

- \`images/\` - Development images (logos, banners, etc.)
- \`icons/\` - Development icons (favicons, etc.)

## Usage

Assets in this folder are for development purposes only and should not be deployed to production until they are ready.

To use these assets in the production site:
1. Test them thoroughly in the development environment
2. Move them to the appropriate production folders (e.g., \`public/\`)
3. Update references in the code as needed
`;

fs.writeFileSync('dev/README.md', devReadmeContent);
console.log('✅ Created dev/README.md');

// Update package.json
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Add scripts
  packageJson.scripts = packageJson.scripts || {};
  packageJson.scripts['use:dev'] = 'node dev-mode.js dev';
  packageJson.scripts['use:prod'] = 'node dev-mode.js prod';
  
  // Add cross-env dependency if not present
  packageJson.devDependencies = packageJson.devDependencies || {};
  if (!packageJson.devDependencies['cross-env']) {
    packageJson.devDependencies['cross-env'] = '^7.0.3';
  }
  
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  console.log('✅ Updated package.json');
  
  console.log('\n🔍 Installing dependencies...');
  console.log('Run the following command to install dependencies:');
  console.log('npm install cross-env --save-dev');
} else {
  console.error('❌ package.json not found');
}

// Update .gitignore
const gitignoreContent = `
# development files
/dev-temp/
*.dev.js
*.dev.ts
*.dev.tsx
.env.development
`;

if (fs.existsSync('.gitignore')) {
  fs.appendFileSync('.gitignore', gitignoreContent);
  console.log('✅ Updated .gitignore');
} else {
  fs.writeFileSync('.gitignore', gitignoreContent);
  console.log('✅ Created .gitignore');
}

console.log('\n🚀 Development environment setup complete!');
console.log('\nNext steps:');
console.log('1. Install dependencies: npm install cross-env --save-dev');
console.log('2. Update your layout component to include the DevIndicator');
console.log('3. Switch to development mode: npm run use:dev');
console.log('4. Start the development server: npm run dev');

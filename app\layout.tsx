import './globals.css';
import './enhanced-styles.css';
import dynamic from 'next/dynamic';

// Dynamically import the DevIndicator to avoid SSR issues
const DevIndicator = dynamic(() => import('../components/DevIndicator'), {
  ssr: false
});

export const metadata = {
  title: 'IMPAXX – Osez transformer',
  description: 'Solutions numériques audacieuses pour PME ambitieuses',
  icons: {
    icon: [
      { url: '/icons/favicon.ico', sizes: 'any' }
    ],
    shortcut: '/icons/favicon.ico',
    apple: '/icons/apple-icon.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr">
      <head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
        <link rel="icon" href="/icons/favicon.ico" sizes="any" />
        <link rel="shortcut icon" href="/icons/favicon.ico" type="image/x-icon" />
        <link rel="apple-touch-icon" href="/icons/apple-icon.png" />
        <meta name="theme-color" content="#0b1a24" />
      </head>
      <body>
        {children}
        <DevIndicator />
      </body>
    </html>
  )
}

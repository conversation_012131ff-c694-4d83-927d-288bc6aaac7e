# IMPAXX Website

This is the official website for IMPAXX, a digital transformation consultancy.

## Features

- Next.js (React) frontend
- TailwindCSS for styling
- Responsive design
- Multilingual support (French/English)

## Development Workflow

This project uses a special workflow to allow development without affecting the live production site.

### Getting Started

1. **Switch to Development Mode**:
   ```bash
   npm run use:dev
   ```
   This will configure the project for development.

2. **Start the Development Server**:
   ```bash
   npm run dev
   ```
   This will start the development server on http://localhost:3000.

3. **Make Your Changes**:
   - All development assets should be placed in the `/dev` folder first
   - Test thoroughly in development mode
   - When ready, move assets to the appropriate production folders

4. **Switch Back to Production Mode**:
   ```bash
   npm run use:prod
   ```
   This will restore the production configuration.

5. **Build and Test Production**:
   ```bash
   npm run build
   npm start
   ```

6. **Deploy to Production**:
   ```bash
   git add .
   git commit -m "Your commit message"
   git push origin main
   ```
   This will trigger a deployment on Vercel.

### Development vs. Production

- Development mode shows a "DEVELOPMENT MODE" indicator in the bottom right corner
- Development assets are stored in the `/dev` folder
- Production assets are stored in the `/public` folder
- The main branch is connected to the production site

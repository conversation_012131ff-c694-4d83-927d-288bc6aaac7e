# Development Environment Template

This template provides a structured development environment for Next.js projects, allowing you to work on features without affecting the production site.

## Features

- Development mode indicator
- Easy switching between development and production modes
- Organized folder structure for assets
- Environment-specific configuration

## Installation

### For New Projects

1. Create a new Next.js project:
   ```bash
   npx create-next-app my-project
   cd my-project
   ```

2. Copy the template files to your project:
   - Copy all files from this template folder to your project root
   - Run the setup script:
     ```bash
     node setup-dev-environment.js
     ```

3. Install dependencies:
   ```bash
   npm install cross-env --save-dev
   ```

4. Update your layout component to include the DevIndicator:

   ```jsx
   import dynamic from 'next/dynamic';

   // Dynamically import the DevIndicator to avoid SSR issues
   const DevIndicator = dynamic(() => import('../components/DevIndicator'), {
     ssr: false
   });

   export default function Layout({ children }) {
     return (
       <div>
         {children}
         <DevIndicator />
       </div>
     );
   }
   ```

5. Switch to development mode:
   ```bash
   npm run use:dev
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

### For Existing Projects

1. Copy the template files to your project:
   - Copy all files from this template folder to your project root
   - Run the setup script:
     ```bash
     node setup-dev-environment.js
     ```

2. Install dependencies:
   ```bash
   npm install cross-env --save-dev
   ```

3. Update your layout component to include the DevIndicator (see step 4 above)

4. Switch to development mode:
   ```bash
   npm run use:dev
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```

## Usage

### Folder Structure

```
/
├── components/
│   └── DevIndicator.tsx  # Development mode indicator component
├── dev/                  # Development assets (not for production)
│   ├── images/           # Development images
│   └── icons/            # Development icons
├── public/               # Production-ready assets
│   ├── images/           # Production images
│   └── icons/            # Production icons
├── .env.development      # Development environment variables
├── .env.production       # Production environment variables
├── dev-mode.js           # Script to switch between dev and prod modes
└── next.config.dev.js    # Development-specific Next.js configuration
```

### Development Workflow

1. **Switch to Development Mode**:
   ```bash
   npm run use:dev
   ```

2. **Start the Development Server**:
   ```bash
   npm run dev
   ```

3. **Work on Features**:
   - Place development assets in the `/dev` folder
   - When ready, move them to the appropriate production folders

4. **Switch to Production Mode**:
   ```bash
   npm run use:prod
   ```

5. **Build and Test Production**:
   ```bash
   npm run build
   npm start
   ```

6. **Deploy to Production**:
   ```bash
   git add .
   git commit -m "Your commit message"
   git push
   ```

## Notes

- The development mode indicator will only appear when in development mode
- Assets in the `/dev` folder are not automatically included in production builds
- Keep the folder structure organized to maintain a clean project

import React from 'react';

const DevIndicator = () => {
  // Only show in development environment
  if (process.env.NODE_ENV !== 'development' && process.env.NEXT_PUBLIC_ENVIRONMENT !== 'development') {
    return null;
  }

  return (
    <div 
      style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        backgroundColor: '#FF6B00',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '4px',
        fontWeight: 'bold',
        zIndex: 9999,
        boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
      }}
    >
      DEVELOPMENT MODE
    </div>
  );
};

export default DevIndicator;

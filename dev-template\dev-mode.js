/**
 * Development Mode Helper Script
 * 
 * This script helps switch between development and production configurations
 * without affecting the live site.
 */

const fs = require('fs');
const path = require('path');

// Check command line arguments
const args = process.argv.slice(2);
const mode = args[0] || 'dev'; // Default to dev mode

if (mode === 'dev') {
  console.log('Switching to DEVELOPMENT mode...');
  
  // Use development config
  if (fs.existsSync('next.config.dev.js')) {
    const devConfig = fs.readFileSync('next.config.dev.js', 'utf8');
    fs.writeFileSync('next.config.js', devConfig);
    console.log('✅ Using development Next.js configuration');
  }
  
  // Set environment
  if (!fs.existsSync('.env.development')) {
    fs.writeFileSync('.env.development', 'NEXT_PUBLIC_ENVIRONMENT=development\n');
    console.log('✅ Created development environment file');
  }
  
  console.log('\n🚀 Development mode activated!');
  console.log('Run "npm run dev" to start the development server');
  
} else if (mode === 'prod') {
  console.log('Switching to PRODUCTION mode...');
  
  // Restore production config if it exists
  if (fs.existsSync('next.config.prod.js')) {
    const prodConfig = fs.readFileSync('next.config.prod.js', 'utf8');
    fs.writeFileSync('next.config.js', prodConfig);
    console.log('✅ Using production Next.js configuration');
  } else {
    // Create a basic production config
    const prodConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  trailingSlash: false,
  images: {
    domains: [],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
}

module.exports = nextConfig`;
    
    // Save the current config as prod config
    fs.writeFileSync('next.config.prod.js', prodConfig);
    fs.writeFileSync('next.config.js', prodConfig);
    console.log('✅ Created and using production Next.js configuration');
  }
  
  console.log('\n🚀 Production mode activated!');
  console.log('Run "npm run build" followed by "npm start" to use production mode');
  
} else {
  console.error('❌ Invalid mode. Use "dev" or "prod"');
  process.exit(1);
}

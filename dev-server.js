const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.json': 'application/json'
};

function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(res, filePath) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end('<h1>404 - File Not Found</h1>');
            return;
        }
        
        const mimeType = getMimeType(filePath);
        res.writeHead(200, { 'Content-Type': mimeType });
        res.end(data);
    });
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;
    
    // Add CORS headers for development
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    console.log(`Request: ${req.method} ${pathname}`);
    
    // Route handling
    if (pathname === '/' || pathname === '/index.html') {
        // Serve the main HTML file
        serveFile(res, path.join(__dirname, 'dev', 'impaxx_fixed_new.html'));
    } else if (pathname === '/legal' || pathname === '/legal.html') {
        // Serve the legal page
        serveFile(res, path.join(__dirname, 'dev', 'impaxx_legal.html'));
    } else if (pathname.startsWith('/dev/')) {
        // Serve files from dev directory
        const filePath = path.join(__dirname, pathname);
        serveFile(res, filePath);
    } else if (pathname.startsWith('/service') && pathname.endsWith('.jpg')) {
        // Serve service images from dev directory
        const fileName = path.basename(pathname);
        const filePath = path.join(__dirname, 'dev', fileName);
        serveFile(res, filePath);
    } else {
        // Try to serve the file from the current directory
        const filePath = path.join(__dirname, pathname);
        
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                // File doesn't exist, return 404
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <h1>404 - Page Not Found</h1>
                    <p>Available routes:</p>
                    <ul>
                        <li><a href="/">Home (impaxx_fixed_new.html)</a></li>
                        <li><a href="/legal">Legal Page (impaxx_legal.html)</a></li>
                    </ul>
                `);
            } else {
                serveFile(res, filePath);
            }
        });
    }
});

server.listen(PORT, () => {
    console.log('🚀 IMPAXX Development Server Started');
    console.log('=====================================');
    console.log(`📍 Server running at: http://localhost:${PORT}`);
    console.log('📄 Available pages:');
    console.log(`   • Main page: http://localhost:${PORT}/`);
    console.log(`   • Legal page: http://localhost:${PORT}/legal`);
    console.log('🖼️  Service images: service1.jpg, service2.jpg, service3.jpg, service4.jpg');
    console.log('=====================================');
    console.log('Press Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down development server...');
    server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
    });
});

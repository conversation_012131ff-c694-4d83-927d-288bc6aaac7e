import React from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the DevIndicator to avoid SSR issues
const DevIndicator = dynamic(() => import('../components/DevIndicator'), {
  ssr: false
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* Your head content */}
      </head>
      <body>
        {children}
        <DevIndicator />
      </body>
    </html>
  );
}
